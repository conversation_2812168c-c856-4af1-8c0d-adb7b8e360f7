/**
 * 江西干部网络学院自动学习助手 - 内容脚本样式
 * 悬浮日志窗口和按钮的样式
 */

/* 悬浮按钮样式 */
.logger-floating-button {
    position: fixed !important;
    left: 20px !important;
    bottom: 20px !important;
    width: 64px !important;
    height: 64px !important;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
    color: white !important;
    font-size: 24px !important;
    border: none !important;
    outline: none !important;
    user-select: none !important;
    text-align: center !important;
    line-height: 1 !important;
}

.logger-floating-button:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6) !important;
}

.logger-floating-button:active {
    transform: scale(0.95) !important;
}

/* 悬浮窗口样式 */
.logger-floating-window {
    position: fixed !important;
    left: 20px !important;
    bottom: 100px !important;
    width: 400px !important;
    height: 300px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
    z-index: 10000 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    resize: both !important;
    min-width: 300px !important;
    min-height: 200px !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* 窗口标题栏 */
.logger-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
    padding: 12px 16px !important;
    cursor: move !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    user-select: none !important;
}

.logger-header button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    transition: background 0.2s ease !important;
}

.logger-header button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
}

/* 日志内容区域 */
.logger-content {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 12px !important;
    background: #1f2937 !important;
    font-family: 'Courier New', monospace !important;
    font-size: 11px !important;
    line-height: 1.4 !important;
    color: #e5e7eb !important;
    max-height: 250px !important;
}

.logger-content::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}

.logger-content::-webkit-scrollbar-track {
    background: #374151 !important;
    border-radius: 4px !important;
}

.logger-content::-webkit-scrollbar-thumb {
    background: #4facfe !important;
    border-radius: 4px !important;
    border: 1px solid #374151 !important;
}

.logger-content::-webkit-scrollbar-thumb:hover {
    background: #3b82f6 !important;
}

.logger-content::-webkit-scrollbar-corner {
    background: #374151 !important;
}

/* 日志条目样式 */
.logger-content div {
    margin-bottom: 4px !important;
    display: flex !important;
    gap: 8px !important;
}

.logger-content span:first-child {
    color: #6b7280 !important;
    flex-shrink: 0 !important;
}

.logger-content span:nth-child(2) {
    flex-shrink: 0 !important;
    font-weight: 500 !important;
    width: 50px !important;
}

.logger-content span:nth-child(3) {
    color: #e5e7eb !important;
    flex: 1 !important;
}

/* 状态指示器 */
.auto-learning-status {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    z-index: 9998 !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #374151 !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.auto-learning-status .status-dot {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background: #4ade80 !important;
    animation: pulse 2s infinite !important;
}

.auto-learning-status.running .status-dot {
    background: #f59e0b !important;
}

.auto-learning-status.error .status-dot {
    background: #ef4444 !important;
    animation: none !important;
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1 !important; 
    }
    50% { 
        opacity: 0.5 !important; 
    }
}

/* 进度指示器 */
.learning-progress {
    position: fixed !important;
    top: 60px !important;
    right: 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 8px !important;
    padding: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    z-index: 9997 !important;
    min-width: 200px !important;
    font-size: 12px !important;
    color: #374151 !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.learning-progress .progress-title {
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    color: #1f2937 !important;
}

.learning-progress .progress-item {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 4px !important;
}

.learning-progress .progress-bar {
    width: 100% !important;
    height: 4px !important;
    background: #e5e7eb !important;
    border-radius: 2px !important;
    overflow: hidden !important;
    margin-top: 8px !important;
}

.learning-progress .progress-fill {
    height: 100% !important;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    transition: width 0.3s ease !important;
}

/* 操作提示 */
.operation-hint {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 16px 24px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    z-index: 10001 !important;
    backdrop-filter: blur(10px) !important;
    animation: fadeInOut 2s ease-in-out !important;
}

@keyframes fadeInOut {
    0%, 100% { 
        opacity: 0 !important; 
        transform: translate(-50%, -50%) scale(0.9) !important; 
    }
    50% { 
        opacity: 1 !important; 
        transform: translate(-50%, -50%) scale(1) !important; 
    }
}

/* 高亮元素 */
.auto-learning-highlight {
    outline: 2px solid #4facfe !important;
    outline-offset: 2px !important;
    background: rgba(79, 172, 254, 0.1) !important;
    transition: all 0.3s ease !important;
}

/* 遮罩层 */
.auto-learning-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.3) !important;
    z-index: 9996 !important;
    backdrop-filter: blur(2px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.auto-learning-overlay .loading-spinner {
    width: 40px !important;
    height: 40px !important;
    border: 3px solid rgba(255, 255, 255, 0.3) !important;
    border-top: 3px solid #4facfe !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { 
        transform: rotate(0deg) !important; 
    }
    100% { 
        transform: rotate(360deg) !important; 
    }
}

/* 确保插件样式不被页面样式覆盖 */
.logger-floating-button,
.logger-floating-window,
.auto-learning-status,
.learning-progress,
.operation-hint,
.auto-learning-overlay {
    all: initial !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .logger-floating-button {
        width: 56px !important;
        height: 56px !important;
        font-size: 20px !important;
    }
    
    .logger-floating-window {
        min-width: 280px !important;
        min-height: 180px !important;
    }
    
    .auto-learning-status,
    .learning-progress {
        right: 10px !important;
        font-size: 11px !important;
    }
    
    .learning-progress {
        min-width: 180px !important;
    }
}

/* 打印时隐藏 */
@media print {
    .logger-floating-button,
    .logger-floating-window,
    .auto-learning-status,
    .learning-progress,
    .operation-hint,
    .auto-learning-overlay {
        display: none !important;
    }
}
