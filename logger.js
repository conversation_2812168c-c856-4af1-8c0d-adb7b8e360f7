/**
 * 江西干部网络学院自动学习助手 - 日志管理器
 * 提供统一的日志记录和显示功能
 */

// 避免重复声明
if (typeof window !== 'undefined' && window.logger) {
    // Logger实例已存在，跳过重复创建
} else {

class Logger {
    constructor() {
        this.logs = [];
        this.maxLogs = 500;
        this.floatingWindow = null;
        this.floatingButton = null;
        this.isVisible = false;
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.defaultPosition = { x: 20, y: 20 };
        this.windowPosition = { ...this.defaultPosition };

        this.init();
    }

    async init() {
        await this.loadLogs();
        this.loadCSS();
        this.createFloatingButton();
        this.createFloatingWindow();
        this.setupEventListeners();

        // 默认显示悬浮日志窗
        setTimeout(() => {
            this.showWindow();
        }, 1500);
    }

    // 加载CSS样式
    loadCSS() {
        // 检查是否已经加载了CSS
        if (document.querySelector('#auto-learning-logger-css')) {
            return;
        }

        const link = document.createElement('link');
        link.id = 'auto-learning-logger-css';
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = chrome.runtime.getURL('logger.css');
        document.head.appendChild(link);
    }

    // 加载历史日志
    async loadLogs() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const result = await chrome.storage.local.get(['logs']);
                this.logs = result.logs || [];
            }
        } catch (error) {
            console.error('加载日志失败:', error);
        }
    }

    // 保存日志
    async saveLogs() {
        try {
            // 同时保存到localStorage和chrome.storage
            const logsToSave = this.logs.slice(-this.maxLogs);

            // 保存到localStorage
            localStorage.setItem('autoLearning_logs', JSON.stringify(logsToSave));

            // 尝试保存到chrome.storage
            if (typeof chrome !== 'undefined' && chrome.storage) {
                try {
                    await chrome.storage.local.set({ logs: logsToSave });
                } catch (e) {
                    console.warn('保存到chrome.storage失败:', e);
                }
            }
        } catch (error) {
            console.error('保存日志失败:', error);
        }
    }

    // 添加日志
    async log(message, level = 'info', category = 'general') {
        const logEntry = {
            id: Date.now() + Math.random(),
            time: typeof Utils !== 'undefined' ? Utils.formatTime() : new Date().toLocaleTimeString('zh-CN', { hour12: false }),
            level,
            category,
            message,
            timestamp: Date.now()
        };

        this.logs.push(logEntry);

        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }

        // 更新显示
        this.updateDisplay();

        // 保存到存储
        await this.saveLogs();

        // 控制台输出
        this.consoleLog(logEntry);
    }

    // 控制台输出
    consoleLog(logEntry) {
        const style = this.getConsoleStyle(logEntry.level);
        console.log(
            `%c[${logEntry.time}] %c${logEntry.level.toUpperCase()}%c ${logEntry.message}`,
            'color: #666;',
            style,
            'color: inherit;'
        );
    }

    // 获取控制台样式
    getConsoleStyle(level) {
        const styles = {
            info: 'color: #3b82f6; font-weight: bold;',
            success: 'color: #10b981; font-weight: bold;',
            warning: 'color: #f59e0b; font-weight: bold;',
            error: 'color: #ef4444; font-weight: bold;'
        };
        return styles[level] || styles.info;
    }

    // 快捷方法
    info(message, category) {
        return this.log(message, 'info', category);
    }

    success(message, category) {
        return this.log(message, 'success', category);
    }

    warning(message, category) {
        return this.log(message, 'warning', category);
    }

    error(message, category) {
        return this.log(message, 'error', category);
    }

    // 创建悬浮按钮
    createFloatingButton() {
        // 移除已存在的按钮
        const existingButton = document.querySelector('.auto-learning-log-button');
        if (existingButton) {
            existingButton.remove();
        }

        this.floatingButton = document.createElement('div');
        this.floatingButton.className = 'auto-learning-log-button';
        this.floatingButton.innerHTML = '📋';
        this.floatingButton.title = '显示/隐藏运行日志';

        // 添加事件监听
        this.floatingButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleWindow();
        });

        // 添加悬停效果
        this.floatingButton.addEventListener('mouseenter', () => {
            this.floatingButton.style.transform = 'scale(1.1)';
        });

        this.floatingButton.addEventListener('mouseleave', () => {
            this.floatingButton.style.transform = 'scale(1)';
        });

        document.body.appendChild(this.floatingButton);
    }

    // 创建悬浮窗
    createFloatingWindow() {
        // 移除已存在的窗口
        const existingWindow = document.querySelector('.auto-learning-log-window');
        if (existingWindow) {
            existingWindow.remove();
        }

        // 创建日志窗口
        this.floatingWindow = document.createElement('div');
        this.floatingWindow.className = 'auto-learning-log-window';

        // 设置初始位置和尺寸
        Object.assign(this.floatingWindow.style, {
            left: this.defaultPosition.x + 'px',
            bottom: this.defaultPosition.y + 'px',
            width: '450px',
            height: '380px',
            display: 'none'
        });

        // 创建标题栏
        const header = document.createElement('div');
        header.className = 'auto-learning-log-header';

        const title = document.createElement('span');
        title.textContent = '📋 运行日志';
        header.appendChild(title);

        const controls = document.createElement('div');
        controls.style.cssText = 'display: flex; gap: 10px;';

        const clearBtn = document.createElement('button');
        clearBtn.textContent = '清除';
        clearBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.clearLogs();
        });

        const closeBtn = document.createElement('button');
        closeBtn.textContent = '×';
        closeBtn.className = 'logger-close-btn';
        closeBtn.style.cssText = `
            background: rgba(255, 255, 255, 0.2) !important;
            border: none !important;
            color: white !important;
            padding: 6px 10px !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 16px !important;
            font-weight: bold !important;
            line-height: 1 !important;
            min-width: 28px !important;
            height: 28px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        `;

        // 简化的关闭事件处理
        const self = this; // 保存this引用
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            // 强制设置样式，使用!important确保优先级
            if (self.floatingWindow) {
                // 尝试多种方法确保窗口关闭
                self.floatingWindow.style.setProperty('display', 'none', 'important');
                self.floatingWindow.style.setProperty('visibility', 'hidden', 'important');
                self.floatingWindow.style.setProperty('opacity', '0', 'important');
                self.floatingWindow.style.setProperty('z-index', '-9999', 'important');
                self.floatingWindow.style.setProperty('pointer-events', 'none', 'important');

                // 如果样式设置无效，尝试更强制的隐藏方法
                setTimeout(() => {
                    if (self.floatingWindow && (self.floatingWindow.offsetWidth > 0 || self.floatingWindow.offsetHeight > 0)) {
                        // 不移除元素，而是使用更强制的隐藏方法
                        self.floatingWindow.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; position: fixed !important; left: -9999px !important; top: -9999px !important; z-index: -9999 !important; pointer-events: none !important;';

                        // 如果还是无效，最后才移除
                        setTimeout(() => {
                            if (self.floatingWindow && (self.floatingWindow.offsetWidth > 0 || self.floatingWindow.offsetHeight > 0)) {
                                self.floatingWindow.remove();
                                self.floatingWindow = null;
                            }
                        }, 100);
                    }
                }, 100);
            }
            if (self.floatingButton) {
                self.floatingButton.style.setProperty('display', 'flex', 'important');
                self.floatingButton.style.setProperty('visibility', 'visible', 'important');
                self.floatingButton.style.setProperty('opacity', '1', 'important');
            }
            self.isVisible = false;
        }, true);

        // 防止拖拽干扰
        closeBtn.addEventListener('mousedown', (e) => {
            e.stopPropagation();
        }, true);

        // 添加悬停效果
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.3)';
        });
        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
        });

        controls.appendChild(clearBtn);
        controls.appendChild(closeBtn);
        header.appendChild(controls);

        // 创建日志内容区
        const content = document.createElement('div');
        content.className = 'auto-learning-log-content';

        this.floatingWindow.appendChild(header);
        this.floatingWindow.appendChild(content);
        document.body.appendChild(this.floatingWindow);

        // 使窗口可拖拽
        this.setupDragEvents(header);
    }

    // 设置拖拽事件
    setupDragEvents(header) {
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };

        const handleMouseDown = (e) => {
            // 检查是否点击的是按钮或按钮内的元素
            if (e.target.tagName === 'BUTTON' ||
                e.target.closest('button') ||
                e.target.classList.contains('logger-close-btn') ||
                e.target.closest('.logger-close-btn')) {
                console.log('点击了按钮，不启动拖拽');
                return;
            }

            console.log('开始拖拽');
            isDragging = true;
            dragStart.x = e.clientX - this.floatingWindow.offsetLeft;
            dragStart.y = e.clientY - this.floatingWindow.offsetTop;

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            e.preventDefault();
        };

        const handleMouseMove = (e) => {
            if (!isDragging) return;

            const newX = e.clientX - dragStart.x;
            const newY = e.clientY - dragStart.y;

            // 边界检查
            const maxX = window.innerWidth - this.floatingWindow.offsetWidth;
            const maxY = window.innerHeight - this.floatingWindow.offsetHeight;

            const finalX = Math.max(0, Math.min(newX, maxX));
            const finalY = Math.max(0, Math.min(newY, maxY));

            this.floatingWindow.style.left = finalX + 'px';
            this.floatingWindow.style.top = finalY + 'px';
            this.floatingWindow.style.bottom = 'auto';
        };

        const handleMouseUp = () => {
            isDragging = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        header.addEventListener('mousedown', handleMouseDown);
    }

    // 切换窗口显示/隐藏
    toggleWindow() {
        if (this.isVisible) {
            this.hideWindow();
        } else {
            this.showWindow();
        }
    }

    // 显示窗口
    showWindow() {
        // 如果窗口不存在，重新创建
        if (!this.floatingWindow) {
            this.createFloatingWindow();
        }

        if (this.floatingWindow) {
            // 重置到默认位置
            this.windowPosition = { ...this.defaultPosition };
            this.floatingWindow.style.left = this.defaultPosition.x + 'px';
            this.floatingWindow.style.bottom = this.defaultPosition.y + 'px';
            this.floatingWindow.style.top = 'auto';

            // 强制设置显示样式
            this.floatingWindow.style.setProperty('display', 'flex', 'important');
            this.floatingWindow.style.setProperty('visibility', 'visible', 'important');
            this.floatingWindow.style.setProperty('opacity', '1', 'important');
            this.floatingWindow.style.setProperty('z-index', '10000', 'important');
            this.floatingWindow.style.setProperty('pointer-events', 'auto', 'important');
        }

        if (this.floatingButton) {
            this.floatingButton.style.setProperty('display', 'none', 'important');
            this.floatingButton.style.setProperty('visibility', 'hidden', 'important');
        }

        this.isVisible = true;
        this.updateDisplay();

        // 滚动到底部
        setTimeout(() => {
            if (this.floatingWindow) {
                const content = this.floatingWindow.querySelector('.auto-learning-log-content');
                if (content) {
                    content.scrollTop = content.scrollHeight;
                }
            }
        }, 100);
    }

    // 隐藏窗口
    hideWindow() {
        try {
            if (this.floatingWindow) {
                // 使用强制样式设置
                this.floatingWindow.style.setProperty('display', 'none', 'important');
                this.floatingWindow.style.setProperty('visibility', 'hidden', 'important');
                this.floatingWindow.style.setProperty('opacity', '0', 'important');
            }

            if (this.floatingButton) {
                this.floatingButton.style.setProperty('display', 'flex', 'important');
                this.floatingButton.style.setProperty('visibility', 'visible', 'important');
                this.floatingButton.style.setProperty('opacity', '1', 'important');
            }

            this.isVisible = false;
        } catch (error) {
            console.error('隐藏窗口时出错:', error);
        }
    }

    // 更新显示
    updateDisplay() {
        if (!this.isVisible || !this.floatingWindow) return;

        const content = this.floatingWindow.querySelector('.auto-learning-log-content');
        if (!content) return;

        if (this.logs.length === 0) {
            content.innerHTML = '<div class="log-empty">暂无日志记录</div>';
            return;
        }

        const shouldScrollToBottom = content.scrollTop + content.clientHeight >= content.scrollHeight - 10;

        content.innerHTML = this.logs.slice(-100).map(log => `
            <div class="log-entry ${log.level}">
                <div class="log-content">
                    <span class="log-time">${log.time}</span>
                    <span class="log-message">${this.escapeHtml(log.message)}</span>
                </div>
            </div>
        `).join('');

        if (shouldScrollToBottom) {
            setTimeout(() => {
                content.scrollTop = content.scrollHeight;
            }, 10);
        }
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 获取级别图标
    getLevelIcon(level) {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌'
        };
        return icons[level] || icons.info;
    }

    // 获取级别样式
    getLevelStyle(level) {
        const styles = {
            info: 'color: #3b82f6;',
            success: 'color: #10b981;',
            warning: 'color: #f59e0b;',
            error: 'color: #ef4444;'
        };
        return styles[level] || styles.info;
    }

    // 获取日志行样式
    getLogRowStyle(level) {
        const styles = {
            info: 'background: linear-gradient(90deg, rgba(59, 130, 246, 0.15) 0%, rgba(59, 130, 246, 0.05) 100%); border-left: 4px solid #3b82f6;',
            success: 'background: linear-gradient(90deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.05) 100%); border-left: 4px solid #10b981;',
            warning: 'background: linear-gradient(90deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.05) 100%); border-left: 4px solid #f59e0b;',
            error: 'background: linear-gradient(90deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.05) 100%); border-left: 4px solid #ef4444;'
        };
        return styles[level] || styles.info;
    }

    // 清除日志
    async clearLogs() {
        this.logs = [];
        await this.saveLogs();
        this.updateDisplay();
        this.info('日志已清除');
    }

    // 设置事件监听器
    setupEventListeners() {
        // 窗口大小变化时调整位置
        window.addEventListener('resize', () => {
            if (this.floatingWindow && this.windowPosition) {
                const maxLeft = window.innerWidth - this.floatingWindow.offsetWidth;
                const maxTop = window.innerHeight - this.floatingWindow.offsetHeight;

                if (this.windowPosition.x > maxLeft) {
                    this.windowPosition.x = maxLeft;
                    this.floatingWindow.style.left = this.windowPosition.x + 'px';
                }

                if (this.windowPosition.y > maxTop) {
                    this.windowPosition.y = maxTop;
                    this.floatingWindow.style.top = this.windowPosition.y + 'px';
                }
            }
        });
    }

    // 销毁
    destroy() {
        if (this.floatingWindow) {
            this.floatingWindow.remove();
        }
        if (this.floatingButton) {
            this.floatingButton.remove();
        }
    }
}

// 创建全局日志实例
if (typeof window !== 'undefined' && !window.logger) {
    try {
        window.logger = new Logger();
    } catch (error) {
        console.error('创建Logger实例失败:', error);
    }
}

} // 结束条件声明
