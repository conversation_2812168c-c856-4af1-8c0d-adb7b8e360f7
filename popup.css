/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: #333;
    overflow: hidden;
}

.container {
    width: 420px;
    height: 590px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 16px 20px;
    color: white;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 24px;
    height: 24px;
    border-radius: 6px;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-weight: 500;
}

/* Tab导航 */
.tab-nav {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #666;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    background: rgba(79, 172, 254, 0.1);
    color: #4facfe;
}

.tab-btn.active {
    color: #4facfe;
    background: rgba(79, 172, 254, 0.1);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #4facfe;
}

/* 图标样式 */
.icon-user::before { content: '👤'; }
.icon-settings::before { content: '⚙️'; }
.icon-log::before { content: '📋'; }

/* Tab内容 */
.tab-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.tab-panel {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #334155;
}

.card-body {
    padding: 20px;
}

/* 控制按钮 */
.control-buttons {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #64748b;
}

.btn-secondary:hover {
    background: #cbd5e1;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 11px;
}

/* 按钮粒子动画 */
.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.btn-primary:hover .btn-particles::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particle 1s ease-out infinite;
}

@keyframes particle {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 输入组件 */
.input-group {
    margin-bottom: 16px;
}

.input-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #374151;
}

.input-group input,
.input-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 13px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.input-group input:focus,
.input-group textarea:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.input-group textarea {
    resize: vertical;
    min-height: 80px;
    font-family: 'Courier New', monospace;
}

.input-hint {
    font-size: 11px;
    color: #6b7280;
    margin-top: 4px;
}

.input-hint a {
    color: #4facfe;
    text-decoration: none;
}

.input-hint a:hover {
    text-decoration: underline;
}

/* 设置网格 */
.settings-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.setting-item:hover {
    background: #f1f5f9;
}

.setting-info {
    flex: 1;
}

.setting-title {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #374151;
}

.setting-desc {
    display: block;
    font-size: 11px;
    color: #6b7280;
    margin-top: 2px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 账号状态 */
.accounts-status {
    margin-top: 16px;
}

.account-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 12px;
}

.account-phone {
    font-weight: 500;
    color: #374151;
}

.account-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
}

.status-ready { background: #dbeafe; color: #1d4ed8; }
.status-running { background: #fef3c7; color: #d97706; }
.status-completed { background: #d1fae5; color: #059669; }
.status-error { background: #fee2e2; color: #dc2626; }

/* 日志容器 */
.logs-container {
    height: 300px;
    overflow-y: auto;
    background: linear-gradient(180deg, #fafbfc 0%, #f1f5f9 100%);
    border-radius: 12px;
    padding: 16px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

/* 弹窗日志条目样式 */
.popup-log-entry {
    margin-bottom: 6px;
    padding: 10px 14px;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
}

.popup-log-entry::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    border-radius: 0 2px 2px 0;
    transition: width 0.3s ease;
}

.popup-log-entry:hover {
    transform: translateX(3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.popup-log-entry:hover::before {
    width: 5px;
}

/* 弹窗日志级别样式 */
.popup-log-entry.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.06) 0%, rgba(147, 197, 253, 0.03) 100%);
    border-color: rgba(59, 130, 246, 0.12);
}

.popup-log-entry.info::before {
    background: linear-gradient(180deg, #3b82f6 0%, #60a5fa 100%);
}

.popup-log-entry.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.06) 0%, rgba(110, 231, 183, 0.03) 100%);
    border-color: rgba(16, 185, 129, 0.12);
}

.popup-log-entry.success::before {
    background: linear-gradient(180deg, #10b981 0%, #34d399 100%);
}

.popup-log-entry.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.06) 0%, rgba(251, 191, 36, 0.03) 100%);
    border-color: rgba(245, 158, 11, 0.12);
}

.popup-log-entry.warning::before {
    background: linear-gradient(180deg, #f59e0b 0%, #fbbf24 100%);
}

.popup-log-entry.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.06) 0%, rgba(248, 113, 113, 0.03) 100%);
    border-color: rgba(239, 68, 68, 0.12);
}

.popup-log-entry.error::before {
    background: linear-gradient(180deg, #ef4444 0%, #f87171 100%);
}

/* 弹窗日志内容布局 */
.popup-log-content {
    display: flex;
    gap: 14px;
    align-items: flex-start;
}

.popup-log-time {
    color: #64748b;
    flex-shrink: 0;
    font-size: 10px;
    min-width: 60px;
    font-weight: 500;
    opacity: 0.8;
    letter-spacing: 0.3px;
}

.popup-log-message {
    color: #1e293b;
    flex: 1;
    word-break: break-word;
    line-height: 1.5;
    font-weight: 500;
    font-size: 11px;
}

/* 空状态样式 */
.popup-log-empty {
    color: #94a3b8;
    text-align: center;
    padding: 30px 20px;
    font-style: italic;
    font-size: 13px;
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.04) 0%, rgba(203, 213, 225, 0.02) 100%);
    border-radius: 10px;
    border: 1px dashed rgba(148, 163, 184, 0.25);
    margin: 15px 0;
}

.popup-log-empty::before {
    content: '📝';
    display: block;
    font-size: 20px;
    margin-bottom: 6px;
    opacity: 0.6;
}

/* 自定义滚动条 */
.logs-container::-webkit-scrollbar,
.tab-content::-webkit-scrollbar {
    width: 6px;
}

.logs-container::-webkit-scrollbar-track,
.tab-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb {
    background: rgba(79, 172, 254, 0.5);
    border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb:hover,
.tab-content::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 172, 254, 0.7);
}

/* 消息提示 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    pointer-events: none;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px 16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    animation: slideIn 0.3s ease;
    pointer-events: auto;
    border-left: 4px solid;
}

.toast.success { border-left-color: #10b981; }
.toast.error { border-left-color: #ef4444; }
.toast.warning { border-left-color: #f59e0b; }
.toast.info { border-left-color: #3b82f6; }

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-icon {
    font-size: 14px;
}

.toast-message {
    flex: 1;
    color: #374151;
}

.toast-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    font-size: 16px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: #374151;
}
