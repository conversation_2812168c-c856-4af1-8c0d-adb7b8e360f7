/**
 * 江西干部网络学院自动学习助手 - 后台服务
 * 处理跨域请求、消息传递和任务调度
 */

class BackgroundService {
    constructor() {
        this.isRunning = false;
        this.currentAccount = null;
        this.accounts = [];
        this.settings = {};
        this.accessToken = null;
        this.tokenExpiry = 0;
        
        this.init();
    }

    init() {
        this.setupMessageListeners();
        this.setupTabListeners();
        this.loadState();
    }

    // 设置消息监听器
    setupMessageListeners() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });
    }

    // 设置标签页监听器
    setupTabListeners() {
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && this.isRunning) {
                this.handleTabUpdate(tabId, tab);
            }
        });

        chrome.tabs.onRemoved.addListener((tabId) => {
            if (this.isRunning) {
                this.handleTabRemoved(tabId);
            }
        });
    }

    // 处理消息
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'startLearning':
                    await this.startLearning(request.accounts, request.settings);
                    sendResponse({ success: true });
                    break;

                case 'stopLearning':
                    await this.stopLearning();
                    sendResponse({ success: true });
                    break;

                case 'getAccessToken':
                    const token = await this.getAccessToken(request.ak, request.sk);
                    sendResponse({ token });
                    break;

                case 'recognizeCaptcha':
                    const result = await this.recognizeCaptcha(request.image, request.ak, request.sk);
                    sendResponse({ result });
                    break;

                case 'log':
                    await this.addLog(request.message, request.level, request.category);
                    sendResponse({ success: true });
                    break;

                case 'getState':
                    sendResponse({
                        isRunning: this.isRunning,
                        currentAccount: this.currentAccount,
                        accounts: this.accounts
                    });
                    break;

                case 'saveState':
                    this.isRunning = request.isRunning;
                    this.currentAccount = request.currentAccount;
                    this.settings = request.settings;
                    await this.saveState();
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ error: error.message });
        }
    }

    // 处理标签页更新
    async handleTabUpdate(tabId, tab) {
        if (!tab.url || !tab.url.includes('study.jxgbwlxy.gov.cn')) {
            return;
        }

        // 使用setTimeout避免阻塞消息处理
        setTimeout(async () => {
            try {
                // 等待页面完全加载
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 注入内容脚本
                await chrome.scripting.executeScript({
                    target: { tabId },
                    files: ['utils.js', 'logger.js', 'content.js']
                });

                // 注入CSS样式
                await chrome.scripting.insertCSS({
                    target: { tabId },
                    files: ['content.css']
                });

                // 等待脚本初始化
                await new Promise(resolve => setTimeout(resolve, 500));

                // 发送当前状态
                try {
                    await chrome.tabs.sendMessage(tabId, {
                        action: 'updateState',
                        isRunning: this.isRunning,
                        currentAccount: this.currentAccount,
                        settings: this.settings
                    });
                } catch (msgError) {
                    console.warn('发送状态更新消息失败:', msgError);
                }

                // 如果正在运行且有当前账号，继续处理
                if (this.isRunning && this.currentAccount) {
                    try {
                        await chrome.tabs.sendMessage(tabId, {
                            action: 'processAccount',
                            account: this.currentAccount,
                            settings: this.settings
                        });
                    } catch (msgError) {
                        console.warn('发送处理账号消息失败:', msgError);
                    }
                }
            } catch (error) {
                console.error('处理标签页更新失败:', error);
            }
        }, 100);
    }

    // 处理标签页关闭
    handleTabRemoved(tabId) {
        // 如果关闭的是学习页面，可能需要重新打开
        console.log('标签页已关闭:', tabId);
    }

    // 开始学习
    async startLearning(accounts, settings) {
        this.accounts = accounts;
        this.settings = settings;
        this.isRunning = true;
        this.currentAccount = null;

        await this.saveState();
        await this.addLog('开始自动学习', 'info', 'system');

        // 打开登录页面
        try {
            const tab = await chrome.tabs.create({
                url: 'https://study.jxgbwlxy.gov.cn/index',
                active: true
            });

            // 等待页面加载后开始处理
            setTimeout(async () => {
                await this.processNextAccount();
            }, 3000);

        } catch (error) {
            await this.addLog(`打开页面失败: ${error.message}`, 'error', 'system');
            this.isRunning = false;
            await this.saveState();
        }
    }

    // 停止学习
    async stopLearning() {
        this.isRunning = false;
        this.currentAccount = null;
        await this.saveState();
        await this.addLog('已停止学习', 'info', 'system');
    }

    // 处理下一个账号
    async processNextAccount() {
        if (!this.isRunning) return;

        // 找到下一个未完成的账号
        const nextAccount = this.accounts.find(acc => acc.status !== 'completed');
        
        if (!nextAccount) {
            await this.addLog('所有账号学习完成', 'success', 'system');
            await this.stopLearning();
            return;
        }

        this.currentAccount = nextAccount;
        nextAccount.status = 'running';
        await this.saveState();

        await this.addLog(`开始处理账号: ${nextAccount.phone}`, 'info', 'account');

        // 发送消息给内容脚本开始处理当前账号
        try {
            const tabs = await chrome.tabs.query({ 
                url: 'https://study.jxgbwlxy.gov.cn/*' 
            });

            if (tabs.length > 0) {
                await chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'processAccount',
                    account: nextAccount,
                    settings: this.settings
                });
            }
        } catch (error) {
            await this.addLog(`发送消息失败: ${error.message}`, 'error', 'account');
        }
    }

    // 获取百度OCR访问令牌
    async getAccessToken(ak, sk) {
        try {
            // 检查缓存的token是否有效
            if (this.accessToken && Date.now() < this.tokenExpiry) {
                return this.accessToken;
            }

            const response = await fetch(
                `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${ak}&client_secret=${sk}`,
                { method: 'POST' }
            );

            const data = await response.json();
            
            if (data.access_token) {
                this.accessToken = data.access_token;
                this.tokenExpiry = Date.now() + (data.expires_in - 300) * 1000; // 提前5分钟过期
                return this.accessToken;
            } else {
                throw new Error(data.error_description || '获取访问令牌失败');
            }
        } catch (error) {
            console.error('获取访问令牌失败:', error);
            throw error;
        }
    }

    // 识别验证码
    async recognizeCaptcha(imageBase64, ak, sk) {
        try {
            const accessToken = await this.getAccessToken(ak, sk);
            
            const formData = new FormData();
            formData.append('image', imageBase64);
            formData.append('detect_direction', 'false');
            formData.append('paragraph', 'false');
            formData.append('probability', 'false');
            formData.append('multidirectional_recognize', 'false');

            const response = await fetch(
                `https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic?access_token=${accessToken}`,
                {
                    method: 'POST',
                    body: formData
                }
            );

            const data = await response.json();
            
            if (data.words_result && data.words_result.length > 0) {
                // 提取验证码文本，只保留数字和字母
                const text = data.words_result[0].words.replace(/[^a-zA-Z0-9]/g, '');
                return text;
            } else {
                throw new Error('未识别到验证码');
            }
        } catch (error) {
            console.error('验证码识别失败:', error);
            throw error;
        }
    }

    // 添加日志
    async addLog(message, level = 'info', category = 'general') {
        const logEntry = {
            id: Date.now() + Math.random(),
            time: new Date().toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }),
            level,
            category,
            message,
            timestamp: Date.now()
        };

        try {
            // 检查扩展上下文是否有效
            if (!chrome.runtime || !chrome.runtime.id) {
                console.warn('扩展上下文已失效，无法保存日志');
                return;
            }

            const result = await chrome.storage.local.get(['logs']);
            const logs = result.logs || [];
            logs.push(logEntry);

            // 限制日志数量
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }

            await chrome.storage.local.set({ logs });

            // 发送通知给内容脚本
            try {
                const tabs = await chrome.tabs.query({
                    url: 'https://study.jxgbwlxy.gov.cn/*'
                });

                for (const tab of tabs) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {
                            action: 'logUpdated',
                            log: logEntry
                        });
                    } catch (error) {
                        // 标签页可能已关闭，忽略错误
                    }
                }
            } catch (error) {
                // 忽略发送消息的错误
            }

        } catch (error) {
            console.error('保存日志失败:', error);
        }
    }

    // 加载状态
    async loadState() {
        try {
            const result = await chrome.storage.local.get([
                'isRunning', 'currentAccount', 'accounts', 'settings'
            ]);

            this.isRunning = result.isRunning || false;
            this.currentAccount = result.currentAccount || null;
            this.accounts = result.accounts || [];
            this.settings = result.settings || {};
        } catch (error) {
            console.error('加载状态失败:', error);
        }
    }

    // 保存状态
    async saveState() {
        try {
            await chrome.storage.local.set({
                isRunning: this.isRunning,
                currentAccount: this.currentAccount,
                accounts: this.accounts,
                settings: this.settings
            });
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    // 账号完成处理
    async accountCompleted(phone, success = true) {
        const account = this.accounts.find(acc => acc.phone === phone);
        if (account) {
            account.status = success ? 'completed' : 'error';
            await this.saveState();

            if (success) {
                await this.addLog(`账号 ${phone} 学习完成`, 'success', 'account');
            } else {
                await this.addLog(`账号 ${phone} 学习未完成`, 'warning', 'account');
            }

            // 处理下一个账号
            setTimeout(() => {
                this.processNextAccount();
            }, 2000);
        }
    }

    // 发送通知
    async sendNotification(title, message, type = 'basic') {
        try {
            await chrome.notifications.create({
                type: type,
                iconUrl: 'icons/icon48.png',
                title: title,
                message: message
            });
        } catch (error) {
            console.error('发送通知失败:', error);
        }
    }
}

// 初始化后台服务
const backgroundService = new BackgroundService();

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'accountCompleted') {
        backgroundService.accountCompleted(request.phone, request.success);
        sendResponse({ success: true });
    }
    return true;
});
