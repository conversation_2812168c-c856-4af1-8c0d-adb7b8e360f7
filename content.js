/**
 * 江西干部网络学院自动学习助手 - 内容脚本
 * 处理页面自动化操作和学习流程
 */

// 避免重复声明
if (typeof window !== 'undefined' && window.AutoLearningBot) {
    // 如果已经存在，直接使用
} else {

class AutoLearningBot {
    constructor() {
        this.isRunning = false;
        this.currentAccount = null;
        this.settings = {};
        this.retryCount = 0;
        this.maxRetries = 3;
        this.pageHandlers = new Map();
        
        this.init();
    }

    async init() {
        await Utils.waitForPageLoad();
        this.setupPageHandlers();
        this.setupMessageListeners();
        await this.detectAndHandlePage();

        // 初始化日志
        if (window.logger) {
            await window.logger.info('内容脚本已加载', 'system');
        }
    }

    // 获取延时时间（毫秒）
    getDelayTime() {
        // 从设置中获取延时时间（秒），转换为毫秒，默认1秒
        return (this.settings.delayTime || 1) * 1000;
    }

    // 设置页面处理器
    setupPageHandlers() {
        this.pageHandlers.set('login', this.handleLoginPage.bind(this));
        this.pageHandlers.set('data', this.handleDataPage.bind(this));
        this.pageHandlers.set('courseMine', this.handleCoursePage.bind(this));
        this.pageHandlers.set('courseDetails', this.handleCourseDetailsPage.bind(this));
        this.pageHandlers.set('videoChoose', this.handleVideoChoosePage.bind(this));
        this.pageHandlers.set('video', this.handleVideoPage.bind(this));
        this.pageHandlers.set('courseView', this.handleCourseViewPage.bind(this));
    }

    // 设置消息监听器
    setupMessageListeners() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 监听页面跳转 - 使用localStorage保存状态
        window.addEventListener('beforeunload', () => {
            if (this.isRunning) {
                // 页面即将跳转，保存状态到localStorage
                this.saveStateToLocal();
            }
        });

        // 监听页面加载完成
        window.addEventListener('load', () => {
            // 从localStorage恢复状态
            this.loadStateFromLocal();

            if (this.isRunning) {
                // 页面加载完成，继续处理
                setTimeout(() => {
                    this.detectAndHandlePage();
                }, 2000);
            }
        });
    }

    // 处理消息
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'updateState':
                    this.isRunning = request.isRunning;
                    this.currentAccount = request.currentAccount;
                    this.settings = request.settings;
                    sendResponse({ success: true });
                    break;

                case 'processAccount':
                    this.currentAccount = request.account;
                    this.settings = request.settings;
                    this.isRunning = true;
                    await this.startAccountProcessing();
                    sendResponse({ success: true });
                    break;

                case 'logUpdated':
                    // 更新本地日志显示
                    if (window.logger) {
                        window.logger.logs.push(request.log);
                        if (window.logger.isVisible) {
                            window.logger.updateDisplay();
                        }
                    }
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ error: error.message });
        }
    }

    // 检测并处理当前页面
    async detectAndHandlePage() {
        const url = window.location.href;
        const pageInfo = Utils.getPageInfo();
        
        let pageType = 'unknown';
        
        if (url.includes('/index')) {
            pageType = 'login';
        } else if (url.includes('/study/data')) {
            pageType = 'data';
        } else if (url.includes('/study/courseMine')) {
            pageType = 'courseMine';
        } else if (url.includes('/courseDetailsNew')) {
            pageType = 'courseDetails';
        } else if (url.includes('/videoChoose')) {
            pageType = 'videoChoose';
        } else if (url.includes('/video?id=')) {
            pageType = 'video';
        } else if (url.includes('/study/course-view')) {
            pageType = 'courseView';
        }

        if (window.logger) {
            await window.logger.success(`检测到页面类型: ${pageType}`, 'page');
        }

        const handler = this.pageHandlers.get(pageType);
        if (handler && this.isRunning) {
            try {
                await handler();
            } catch (error) {
                if (window.logger) {
                    await window.logger.error(`页面处理失败: ${error.message}`, 'page');
                }
                await this.handleError(error);
            }
        }
    }

    // 开始账号处理
    async startAccountProcessing() {
        if (!this.currentAccount) {
            if (window.logger) {
                await window.logger.error('没有当前账号信息', 'account');
            }
            return;
        }

        if (window.logger) {
            await window.logger.info(`开始处理账号: ${this.currentAccount.phone}`, 'account');
        }

        // 检查学习完成状态（在任何页面都可以检查）
        if (window.logger) {
            await window.logger.info('检查账号学习完成状态', 'account');
        }

        // 等待页面加载完成
        await Utils.sleep(this.getDelayTime());

        // 检查学习状态，如果已完成会自动处理退出逻辑
        const completionStatus = await this.checkCompletionStatus();

        // 如果已完成，checkCompletionStatus 会处理退出，这里直接返回
        if (completionStatus === '已完成') {
            return;
        }

        // 检查当前页面并开始处理
        await this.detectAndHandlePage();
    }

    // 处理登录页面
    async handleLoginPage() {
        if (window.logger) {
            await window.logger.info('处理登录页面', 'login');
        }

        try {
            // 检查是否已经登录（页面自动跳转）
            await Utils.sleep(this.getDelayTime());
            if (window.location.href.includes('/study/data')) {
                if (window.logger) {
                    await window.logger.info('检测到已登录，先退出账号', 'login');
                }
                await this.logout();
                return;
            }

            // 等待登录表单加载
            await Utils.waitForElement('.loginCtr.flex-column.flex-around-center', 10000);

            // 填写账号信息
            await this.fillLoginForm();

            // 处理验证码
            await this.handleCaptcha();

            // 点击登录
            await this.clickLogin();

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`登录失败: ${error.message}`, 'login');
            }
            throw error;
        }
    }

    // 填写登录表单
    async fillLoginForm() {
        if (!this.currentAccount) {
            throw new Error('没有当前账号信息');
        }

        // 填写手机号
        const phoneInput = await Utils.waitForElement('.el-input__inner[placeholder="您的手机号"]');
        await Utils.safeInput(phoneInput, this.currentAccount.phone);
        
        if (window.logger) {
            await window.logger.success(`已填写手机号: ${this.currentAccount.phone}`, 'login');
        }

        // 填写密码
        const passwordInput = await Utils.waitForElement('.el-input__inner[placeholder="请输入密码"]');
        await Utils.safeInput(passwordInput, this.currentAccount.password);
        
        if (window.logger) {
            await window.logger.success('已填写密码', 'login');
        }
    }

    // 处理验证码
    async handleCaptcha() {
        try {
            const captchaImg = await Utils.waitForElement('.yzmImg', 5000);
            const captchaInput = await Utils.waitForElement('.el-input__inner[placeholder="验证码"]');

            // 获取验证码图片的Base64
            const imageBase64 = await Utils.getImageBase64(captchaImg);
            
            if (window.logger) {
                await window.logger.success('开始识别验证码', 'captcha');
            }

            // 调用后台服务识别验证码
            const response = await chrome.runtime.sendMessage({
                action: 'recognizeCaptcha',
                image: imageBase64,
                ak: this.settings.baiduAK,
                sk: this.settings.baiduSK
            });

            if (response.error) {
                throw new Error(response.error);
            }

            const captchaText = response.result;
            if (window.logger) {
                await window.logger.success(`验证码识别结果: ${captchaText}`, 'captcha');
            }

            // 填写验证码
            await Utils.safeInput(captchaInput, captchaText);

        } catch (error) {
            if (window.logger) {
                await window.logger.warning(`验证码处理失败: ${error.message}`, 'captcha');
            }
            
            // 重试机制
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                if (window.logger) {
                    await window.logger.warning(`重试验证码识别 (${this.retryCount}/${this.maxRetries})`, 'captcha');
                }
                
                // 刷新验证码
                const captchaImg = document.querySelector('.yzmImg');
                if (captchaImg) {
                    await Utils.safeClick(captchaImg);
                    await Utils.sleep(this.getDelayTime());
                }
                
                await this.handleCaptcha();
            } else {
                throw new Error('验证码识别失败次数过多');
            }
        }
    }

    // 点击登录
    async clickLogin() {
        const loginBtn = await Utils.waitForElement('.loginBtn.el-button');
        await Utils.safeClick(loginBtn);
        
        if (window.logger) {
            await window.logger.success('已点击登录按钮', 'login');
        }

        // 等待页面跳转
        try {
            await Utils.waitForUrlChange('/study/data', 10000);
            if (window.logger) {
                await window.logger.success('登录成功', 'login');
            }
            this.retryCount = 0; // 重置重试计数
        } catch (error) {
            // 检查是否有错误提示
            await Utils.sleep(this.getDelayTime());
            const errorMsg = await this.checkLoginError();
            if (errorMsg) {
                throw new Error(`登录失败: ${errorMsg}`);
            } else {
                throw new Error('登录超时');
            }
        }
    }

    // 检查登录错误
    async checkLoginError() {
        try {
            // 检查常见的错误提示元素
            const errorSelectors = [
                '.el-message--error',
                '.el-notification__content',
                '.error-message'
            ];

            for (const selector of errorSelectors) {
                const errorElement = document.querySelector(selector);
                if (errorElement && errorElement.textContent.trim()) {
                    return errorElement.textContent.trim();
                }
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    // 退出登录
    async logout() {
        try {
            // // 先点击用户头像或下拉菜单触发器来显示下拉菜单
            // const userDropdown = await Utils.waitForElement('.flex-center.el-dropdown-selfdefined', 5000);
            // if (userDropdown) {
            //     await Utils.safeClick(userDropdown);
            //     await Utils.sleep(1000); // 等待下拉菜单显示

            //     if (window.logger) {
            //         await window.logger.info('已点击用户下拉菜单', 'logout');
            //     }
            // }

            // 查找退出按钮
            const logoutBtn = await Utils.waitForElement('.el-dropdown-menu__item.flex-between-center button:nth-child(2)', 5000);
            await Utils.safeClick(logoutBtn);

            if (window.logger) {
                await window.logger.success('已点击退出按钮', 'logout');
            }

            // 等待跳转到登录页
            await Utils.waitForUrlChange('/index', 5000);

            if (window.logger) {
                await window.logger.success('已成功退出登录', 'logout');
            }

        } catch (error) {
            if (window.logger) {
                await window.logger.warning(`退出登录失败: ${error.message}`, 'logout');
            }
            // 直接跳转到登录页作为备用方案
            window.location.href = 'https://study.jxgbwlxy.gov.cn/index';
        }
    }

    // 处理学习档案页面
    async handleDataPage() {
        if (window.logger) {
            await window.logger.success('处理学习档案页面', 'data');
        }

        try {
            // 等待页面加载完成
            await Utils.sleep(this.getDelayTime());

            // 检查学习状态，如果已完成会自动处理退出逻辑
            const completionStatus = await this.checkCompletionStatus();

            // 如果已完成，checkCompletionStatus 会处理退出，这里直接返回
            if (completionStatus === '已完成') {
                return;
            }

            if (window.logger) {
                await window.logger.success('学习未完成，跳转到我的课程', 'data');
            }

            // 点击"我的课程"菜单
            await this.navigateToMyCourses();

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理学习档案页面失败: ${error.message}`, 'data');
            }
            throw error;
        }
    }

    // 检查完成状态
    async checkCompletionStatus() {
        try {
            const statusElement = await Utils.waitForElement('.el-dropdown-menu__item.is-disabled div div:nth-child(5) b', 5000);
            const statusText = await Utils.getText(statusElement);

            if (window.logger) {
                await window.logger.info(`学习状态: ${statusText}`, 'data');
            }

            // 如果学习已完成，处理账号完成逻辑
            if (statusText === '已完成') {
                if (window.logger) {
                    await window.logger.success(`账号 ${this.currentAccount.phone} 学习已完成`, 'data');
                }

                // 通知后台账号已完成
                await chrome.runtime.sendMessage({
                    action: 'accountCompleted',
                    phone: this.currentAccount.phone,
                    success: true
                });

                // 退出当前账号，切换到下一个账号
                if (window.logger) {
                    await window.logger.info(`账号 ${this.currentAccount.phone} 已完成，准备退出切换下一个账号`, 'data');
                }
                await this.logout();

                return statusText;
            }

            return statusText;
        } catch (error) {
            if (window.logger) {
                await window.logger.warning('无法获取学习状态', 'data');
            }
            return '未知';
        }
    }

    // 导航到我的课程
    async navigateToMyCourses() {
        const myCourseMenu = await Utils.waitForElement('a[href="/study/courseMine?id=0"] .menuLabel');
        await Utils.safeClick(myCourseMenu);
        
        // 等待页面跳转
        await Utils.waitForUrlChange('/study/courseMine', 10000);
    }

    // 处理我的课程页面
    async handleCoursePage() {
        if (window.logger) {
            await window.logger.success('处理我的课程页面', 'course');
        }

        try {
            // 等待页面加载
            await Utils.sleep(this.getDelayTime());

            // 先处理必修课
            const hasRequiredCourse = await this.handleRequiredCourses();

            // 只有在必修课全部完成后才处理选修课
            if (!hasRequiredCourse) {
                await this.handleElectiveCourses();
            }

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理课程页面失败: ${error.message}`, 'course');
            }
            throw error;
        }
    }

    // 处理必修课
    async handleRequiredCourses() {
        if (window.logger) {
            await window.logger.success('处理必修课', 'course');
        }

        // 点击必修课选项卡
        const requiredTab = await Utils.waitForElement('.menu_item .ellipsis', 5000);
        if (requiredTab && requiredTab.textContent.includes('必修')) {
            await Utils.safeClick(requiredTab);
            await Utils.sleep(this.getDelayTime());
        }

        // 查找未完成的课程
        const incompleteCourse = await this.findIncompleteCourse();
        if (incompleteCourse) {
            if (window.logger) {
                await window.logger.success('找到未完成的必修课，开始学习', 'course');
            }
            await Utils.safeClick(incompleteCourse);
            return true;
        }

        if (window.logger) {
            await window.logger.success('必修课已全部完成', 'course');
        }
        return false;
    }

    // 处理选修课
    async handleElectiveCourses() {
        if (window.logger) {
            await window.logger.success('处理选修课', 'course');
        }

        // 点击选修课选项卡
        const allTabs = Utils.getAllElements('.menu_item .ellipsis');

        for (const tab of allTabs) {
            if (tab.textContent.includes('选修')) {
                await Utils.safeClick(tab);
                await Utils.sleep(this.getDelayTime());
                break;
            }
        }

        // 查找未完成的课程
        const incompleteCourse = await this.findIncompleteCourse();
        if (incompleteCourse) {
            if (window.logger) {
                await window.logger.success('找到未完成的选修课，开始学习', 'course');
            }
            await Utils.safeClick(incompleteCourse);
            return true;
        }

        // 如果没有未完成的选修课，且设置了自动选修，则添加课程
        if (this.settings.autoSelectCourse) {
            if (window.logger) {
                await window.logger.warning('没有未完成的选修课，开始添加课程', 'course');
            }
            await this.addElectiveCourses();
        }

        return false;
    }

    // 查找未完成的课程
    async findIncompleteCourse() {
        try {
            const courseCards = Utils.getAllElements('.courseCard.shadow');

            for (const card of courseCards) {
                const statusElement = card.querySelector('.courseCard .red');
                if (statusElement && statusElement.textContent.includes('未完成')) {
                    return card;
                }
            }

            return null;
        } catch (error) {
            if (window.logger) {
                await window.logger.warning(`查找未完成课程失败: ${error.message}`, 'course');
            }
            return null;
        }
    }

    // 添加选修课程
    async addElectiveCourses() {
        try {
            // 跳转到课程分类页面
            const courseCategoryMenu = await Utils.waitForElement('a[href="/study/course-view"] .menuLabel');
            await Utils.safeClick(courseCategoryMenu);

            // 等待页面跳转
            await Utils.waitForUrlChange('/study/course-view', 10000);

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`跳转到课程分类失败: ${error.message}`, 'course');
            }
            throw error;
        }
    }

    // 处理课程分类页面
    async handleCourseViewPage() {
        if (window.logger) {
            await window.logger.success('处理课程分类页面', 'courseView');
        }

        try {
            // 等待页面加载
            await Utils.sleep(this.getDelayTime());

            // 切换到表格视图
            await this.switchToTableView();

            // 添加指定数量的课程
            const targetCount = this.settings.courseCount || 5;
            let addedCount = 0;

            while (addedCount < targetCount) {
                const addButtons = Utils.getAllElements('.el-icon-circle-plus-outline');

                if (addButtons.length === 0) {
                    if (window.logger) {
                        await window.logger.warning('当前页面没有可添加的课程', 'courseView');
                    }
                    break;
                }

                // 添加当前页面的课程
                for (const button of addButtons) {
                    if (addedCount >= targetCount) break;

                    await Utils.safeClick(button);
                    addedCount++;

                    if (window.logger) {
                        await window.logger.success(`已添加课程 ${addedCount}/${targetCount}`, 'courseView');
                    }

                    await Utils.sleep(this.getDelayTime());
                }

                // 如果还需要添加更多课程，翻页
                if (addedCount < targetCount) {
                    const nextPageBtn = document.querySelector('.btn-next');
                    if (nextPageBtn && !nextPageBtn.disabled) {
                        await Utils.safeClick(nextPageBtn);
                        await Utils.sleep(this.getDelayTime());
                    } else {
                        if (window.logger) {
                            await window.logger.warning('没有更多页面，停止添加课程', 'courseView');
                        }
                        break;
                    }
                }
            }

            if (window.logger) {
                await window.logger.success(`课程添加完成，共添加 ${addedCount} 门课程`, 'courseView');
            }

            // 返回我的课程页面，继续处理选修课
            await this.navigateToMyCourses();

            // 等待页面加载后重新处理选修课
            await Utils.sleep(this.getDelayTime());
            await this.handleElectiveCourses();

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理课程分类页面失败: ${error.message}`, 'courseView');
            }
            throw error;
        }
    }

    // 切换到表格视图
    async switchToTableView() {
        try {
            // 检查当前是否为图片视图
            const gridViewBtn = document.querySelector('.el-button--danger.el-button--small.is-plain .el-icon-s-grid');
            if (gridViewBtn) {
                await Utils.safeClick(gridViewBtn);
                await Utils.sleep(this.getDelayTime());

                if (window.logger) {
                    await window.logger.success('已切换到表格视图', 'courseView');
                }
            }
        } catch (error) {
            if (window.logger) {
                await window.logger.warning(`切换视图失败: ${error.message}`, 'courseView');
            }
        }
    }

    // 处理课程详情页面
    async handleCourseDetailsPage() {
        if (window.logger) {
            await window.logger.success('处理课程详情页面', 'courseDetails');
        }

        try {
            // 等待页面加载
            await Utils.sleep(this.getDelayTime());

            // 点击"我要学习"按钮
            const studyBtn = await Utils.waitForElement('.myBtn.selected:nth-child(1)', 10000);
            await Utils.safeClick(studyBtn);

            if (window.logger) {
                await window.logger.success('已点击我要学习按钮', 'courseDetails');
            }

            // 等待页面跳转（可能跳转到温馨提示页面或直接到视频页面）
            await Utils.sleep(this.getDelayTime());

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理课程详情页面失败: ${error.message}`, 'courseDetails');
            }
            throw error;
        }
    }

    // 处理温馨提示页面
    async handleVideoChoosePage() {
        if (window.logger) {
            await window.logger.success('处理温馨提示页面', 'videoChoose');
        }

        try {
            // 等待页面加载
            await Utils.sleep(this.getDelayTime());

            // 点击课件标题
            const coursewareTitle = await Utils.waitForElement('.choose-content', 5000);
            await Utils.safeClick(coursewareTitle);

            if (window.logger) {
                await window.logger.success('已点击课件标题', 'videoChoose');
            }

            // 等待跳转到视频页面
            await Utils.sleep(this.getDelayTime());

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理温馨提示页面失败: ${error.message}`, 'videoChoose');
            }
            throw error;
        }
    }

    // 处理视频页面
    async handleVideoPage() {
        if (window.logger) {
            await window.logger.success('处理视频页面', 'video');
        }

        try {

            // 等待页面加载
            await Utils.sleep(this.getDelayTime());

            // 处理视频播放和静音（考虑浏览器自动播放限制）
            if (this.settings.autoMute) {
                await this.handleVideoPlayAndMute();
            }

            // 监听视频播放和课件切换
            await this.setupVideoMonitoring();

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理视频页面失败: ${error.message}`, 'video');
            }
            throw error;
        }
    }

    // 处理视频播放和静音（增强版）
    async handleVideoPlayAndMute() {
        let retryCount = 0;
        const maxRetries = 3;
        
        while (retryCount < maxRetries) {
            try {
                const video = await Utils.waitForElement('video', 5000);
                if (!video) {
                    if (window.logger) {
                        await window.logger.warning('未找到视频元素', 'video');
                    }
                    return;
                }

                // 检查视频是否已经在播放
                const isPlaying = !video.paused && !video.ended && video.readyState > 2;

                // 如果视频未播放，尝试播放
                if (!isPlaying) {
                    try {
                        // 先静音，然后播放（避免自动播放限制）
                        video.muted = true;
                        await video.play();
                        
                        // 检查播放是否真的开始了
                        await Utils.sleep(1000);
                        if (video.paused) {
                            throw new Error('视频仍处于暂停状态');
                        }
                        
                        if (window.logger) {
                            await window.logger.success('视频已静音并开始播放', 'video');
                        }
                        return; // 成功播放，退出函数
                    } catch (playError) {
                        retryCount++;
                        if (window.logger) {
                            await window.logger.warning(`视频播放失败(${retryCount}/${maxRetries}): ${playError.message}`, 'video');
                        }
                        
                        // 尝试点击播放按钮
                        await this.tryClickPlayButton();
                        await Utils.sleep(2000);
                    }
                } else {
                    // 视频已在播放，直接静音
                    video.muted = true;
                    if (window.logger) {
                        await window.logger.success('视频已静音', 'video');
                    }
                    return; // 成功处理，退出函数
                }
            } catch (error) {
                retryCount++;
                if (window.logger) {
                    await window.logger.warning(`处理视频播放失败(${retryCount}/${maxRetries}): ${error.message}`, 'video');
                }
                await Utils.sleep(2000); // 等待后重试
            }
        }
        
        // 所有重试都失败
        if (window.logger) {
            await window.logger.error('视频播放处理失败，已达到最大重试次数', 'video');
        }
    }

    // 设置视频监听
    async setupVideoMonitoring() {
        try {
            const video = await Utils.waitForElement('video', 5000);
            if (!video) return;

            // 移除可能存在的旧事件监听器，避免重复绑定
            video.removeEventListener('ended', this._handleVideoEnded);
            video.removeEventListener('play', this._handleVideoPlay);
            
            // 使用箭头函数保存上下文，并保存引用以便后续移除
            this._handleVideoEnded = async () => {
                if (window.logger) {
                    await window.logger.info('视频播放结束，检查下一个课件', 'video');
                }
                // 使用Promise.all同时处理等待和日志
                await Promise.all([
                    Utils.sleep(5000),
                    window.logger?.info('等待五秒查找未完成课件', 'video')
                ]);
                await this.handleVideoEnd();
            };
            
            this._handleVideoPlay = async () => {
                if (this.settings.autoMute) {
                    video.muted = true;
                    if (window.logger) {
                        await window.logger.info('视频已静音', 'video');
                    }
                }
            };

            // 添加事件监听器
            video.addEventListener('ended', this._handleVideoEnded);
            video.addEventListener('play', this._handleVideoPlay);
            
            // 添加进度监控，定期检查视频是否卡住
            this._videoProgressInterval = setInterval(() => {
                if (!video.paused && this._lastPlayTime === video.currentTime) {
                    // 视频卡住了，尝试恢复
                    video.currentTime += 1;
                    if (window.logger) {
                        window.logger.warning('检测到视频可能卡住，尝试恢复播放', 'video');
                    }
                }
                this._lastPlayTime = video.currentTime;
            }, 10000); // 每10秒检查一次

            if (window.logger) {
                await window.logger.success('视频监听已设置，包含卡顿检测', 'video');
            }
        } catch (error) {
            if (window.logger) {
                await window.logger.warning(`设置视频监听失败: ${error.message}`, 'video');
            }
        }
    }

    // 处理视频结束
    async handleVideoEnd() {
        try {
            // 查找下一个未完成的课件
            const nextCourseware = await this.findNextCourseware();

            if (nextCourseware) {
                if (window.logger) {
                    await window.logger.success('找到下一个课件，开始播放', 'video');
                }
                await Utils.safeClick(nextCourseware);
                await Utils.sleep(this.getDelayTime());

                // 重新设置静音
                if (this.settings.autoMute) {
                    await this.muteVideo();
                }
            } else {
                if (window.logger) {
                    await window.logger.success('当前课程所有课件已完成', 'video');
                }

                // 返回我的课程页面继续学习
                await this.returnToMyCourses();
            }

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`处理视频结束失败: ${error.message}`, 'video');
            }
        }
    }

    // 查找下一个课件
    async findNextCourseware() {
        try {
            const coursewareList = Utils.getAllElements('.kc_list li');

            for (const item of coursewareList) {
                const statusElement = item.querySelector('.kc-info span:nth-child(3)');
                if (statusElement && statusElement.textContent.includes('未完成')) {
                    return item.querySelector('h5');
                }
            }

            return null;
        } catch (error) {
            if (window.logger) {
                await window.logger.warning(`查找下一个课件失败: ${error.message}`, 'video');
            }
            return null;
        }
    }

    // 返回我的课程页面
    async returnToMyCourses() {
        try {
            // 在当前标签页打开我的课程页面
            window.location.href = 'https://study.jxgbwlxy.gov.cn/study/courseMine?id=0';

            if (window.logger) {
                await window.logger.success('返回我的课程页面，继续学习流程', 'video');
            }

        } catch (error) {
            if (window.logger) {
                await window.logger.error(`返回我的课程页面失败: ${error.message}`, 'video');
            }
        }
    }

    // 保存状态到localStorage
    saveStateToLocal() {
        try {
            const state = {
                isRunning: this.isRunning,
                currentAccount: this.currentAccount,
                settings: this.settings,
                timestamp: Date.now()
            };
            localStorage.setItem('autoLearning_state', JSON.stringify(state));
        } catch (error) {
            console.error('保存状态到localStorage失败:', error);
        }
    }

    // 从localStorage加载状态
    loadStateFromLocal() {
        try {
            const stateStr = localStorage.getItem('autoLearning_state');
            if (stateStr) {
                const state = JSON.parse(stateStr);
                // 检查状态是否过期（30分钟）
                if (Date.now() - state.timestamp < 30 * 60 * 1000) {
                    this.isRunning = state.isRunning || false;
                    this.currentAccount = state.currentAccount || null;
                    this.settings = state.settings || {};
                } else {
                    // 状态过期，清除
                    localStorage.removeItem('autoLearning_state');
                }
            }
        } catch (error) {
            console.error('从localStorage加载状态失败:', error);
        }
    }

    // 保存当前状态（保留原方法，但优先使用localStorage）
    async saveCurrentState() {
        // 直接使用localStorage，避免扩展上下文问题
        this.saveStateToLocal();
    }

    // 安全发送消息
    async safeSendMessage(message, timeout = 3000) {
        try {
            if (!chrome.runtime || !chrome.runtime.id) {
                throw new Error('扩展上下文无效');
            }

            const sendPromise = chrome.runtime.sendMessage(message);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('消息发送超时')), timeout);
            });

            return await Promise.race([sendPromise, timeoutPromise]);
        } catch (error) {
            console.warn('发送消息失败:', error.message);
            throw error;
        }
    }

    // 处理错误
    async handleError(error) {
        if (window.logger) {
            await window.logger.error(`处理错误: ${error.message}`, 'error');
        }

        // 通知后台账号处理失败
        if (this.currentAccount) {
            try {
                await this.safeSendMessage({
                    action: 'accountCompleted',
                    phone: this.currentAccount.phone,
                    success: false
                });
            } catch (e) {
                console.warn('通知后台失败，使用本地处理:', e.message);
                // 本地处理失败情况
                if (window.logger) {
                    await window.logger.warning(`账号 ${this.currentAccount.phone} 学习未完成`, 'account');
                }
            }
        }
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        if (!window.autoLearningBotInstance) {
            window.autoLearningBotInstance = new AutoLearningBot();
        }
    });
} else {
    if (!window.autoLearningBotInstance) {
        window.autoLearningBotInstance = new AutoLearningBot();
    }
}

} // 结束条件声明
