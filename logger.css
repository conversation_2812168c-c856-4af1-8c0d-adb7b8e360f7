/**
 * 江西干部网络学院自动学习助手 - 日志系统样式
 * 专业的悬浮日志窗口和按钮样式
 */

/* 悬浮按钮样式 */
.auto-learning-log-button {
    position: fixed !important;
    left: 20px !important;
    bottom: 20px !important;
    width: 60px !important;
    height: 60px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    color: white !important;
    font-size: 22px !important;
    border: none !important;
    outline: none !important;
    user-select: none !important;
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif !important;
    backdrop-filter: blur(10px) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

.auto-learning-log-button:hover {
    transform: scale(1.15) translateY(-2px) !important;
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6), 0 8px 20px rgba(0, 0, 0, 0.2) !important;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
}

.auto-learning-log-button:active {
    transform: scale(1.05) translateY(0px) !important;
    transition: all 0.15s ease !important;
}

/* 悬浮窗口样式 */
.auto-learning-log-window {
    position: fixed !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: 16px !important;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.12),
        0 8px 25px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.5) !important;
    z-index: 10000 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    border: 1px solid rgba(226, 232, 240, 0.8) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    animation: windowSlideIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

@keyframes windowSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 窗口标题栏 */
.auto-learning-log-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 16px 20px !important;
    cursor: move !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    user-select: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
}

.auto-learning-log-header::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%) !important;
    animation: headerShimmer 3s ease-in-out infinite !important;
}

@keyframes headerShimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.auto-learning-log-header button {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
}

.auto-learning-log-header button:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.auto-learning-log-header button:active {
    transform: translateY(0) !important;
}

/* 日志内容区域 */
.auto-learning-log-content {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 16px !important;
    background: linear-gradient(180deg, #fafbfc 0%, #f1f5f9 100%) !important;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
    font-size: 12px !important;
    line-height: 1.5 !important;
    color: #334155 !important;
    position: relative !important;
}

.auto-learning-log-content::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 1px !important;
    background: linear-gradient(90deg, transparent 0%, rgba(148, 163, 184, 0.3) 50%, transparent 100%) !important;
}

/* 自定义滚动条 */
.auto-learning-log-content::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}

.auto-learning-log-content::-webkit-scrollbar-track {
    background: rgba(148, 163, 184, 0.1) !important;
    border-radius: 4px !important;
    margin: 4px !important;
}

.auto-learning-log-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 4px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.auto-learning-log-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #764ba2 0%, #667eea 100%) !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.auto-learning-log-content::-webkit-scrollbar-corner {
    background: transparent !important;
}

/* 日志条目样式 */
.log-entry {
    margin-bottom: 6px !important;
    padding: 12px 16px !important;
    border-radius: 12px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid transparent !important;
    backdrop-filter: blur(10px) !important;
}

.log-entry::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 4px !important;
    height: 100% !important;
    border-radius: 0 2px 2px 0 !important;
    transition: width 0.3s ease !important;
}

.log-entry:hover {
    transform: translateX(4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08) !important;
}

.log-entry:hover::before {
    width: 6px !important;
}

/* 日志级别样式 */
.log-entry.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.05) 100%) !important;
    border-color: rgba(59, 130, 246, 0.15) !important;
}

.log-entry.info::before {
    background: linear-gradient(180deg, #3b82f6 0%, #60a5fa 100%) !important;
}

.log-entry.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(110, 231, 183, 0.05) 100%) !important;
    border-color: rgba(16, 185, 129, 0.15) !important;
}

.log-entry.success::before {
    background: linear-gradient(180deg, #10b981 0%, #34d399 100%) !important;
}

.log-entry.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(251, 191, 36, 0.05) 100%) !important;
    border-color: rgba(245, 158, 11, 0.15) !important;
}

.log-entry.warning::before {
    background: linear-gradient(180deg, #f59e0b 0%, #fbbf24 100%) !important;
}

.log-entry.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(248, 113, 113, 0.05) 100%) !important;
    border-color: rgba(239, 68, 68, 0.15) !important;
}

.log-entry.error::before {
    background: linear-gradient(180deg, #ef4444 0%, #f87171 100%) !important;
}

/* 日志内容布局 */
.log-content {
    display: flex !important;
    gap: 16px !important;
    align-items: flex-start !important;
}

.log-time {
    color: #64748b !important;
    flex-shrink: 0 !important;
    font-size: 10px !important;
    min-width: 65px !important;
    font-weight: 500 !important;
    opacity: 0.8 !important;
    letter-spacing: 0.5px !important;
}

.log-message {
    color: #1e293b !important;
    flex: 1 !important;
    word-break: break-word !important;
    line-height: 1.6 !important;
    font-weight: 500 !important;
    font-size: 12px !important;
}

/* 空状态样式 */
.log-empty {
    color: #94a3b8 !important;
    text-align: center !important;
    padding: 40px 20px !important;
    font-style: italic !important;
    font-size: 14px !important;
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.05) 0%, rgba(203, 213, 225, 0.03) 100%) !important;
    border-radius: 12px !important;
    border: 1px dashed rgba(148, 163, 184, 0.3) !important;
    margin: 20px 0 !important;
}

.log-empty::before {
    content: '📝' !important;
    display: block !important;
    font-size: 24px !important;
    margin-bottom: 8px !important;
    opacity: 0.6 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auto-learning-log-button {
        width: 52px !important;
        height: 52px !important;
        font-size: 18px !important;
    }
    
    .auto-learning-log-window {
        width: calc(100vw - 40px) !important;
        height: calc(100vh - 100px) !important;
        left: 20px !important;
        top: 50px !important;
    }
    
    .log-content {
        gap: 8px !important;
    }
    
    .log-time {
        min-width: 50px !important;
        font-size: 9px !important;
    }
    
    .log-level {
        width: 50px !important;
        font-size: 10px !important;
    }
    
    .log-message {
        font-size: 11px !important;
    }
}

/* 动画增强 */
@keyframes logEntrySlideIn {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.log-entry {
    animation: logEntrySlideIn 0.3s ease-out !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .auto-learning-log-window {
        border: 2px solid #000 !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    }
    
    .log-entry {
        border: 1px solid #666 !important;
    }
    
    .log-message {
        color: #000 !important;
        font-weight: 600 !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .auto-learning-log-button,
    .auto-learning-log-window,
    .log-entry,
    .auto-learning-log-header button {
        transition: none !important;
        animation: none !important;
    }
}
