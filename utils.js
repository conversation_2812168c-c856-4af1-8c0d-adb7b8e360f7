/**
 * 江西干部网络学院自动学习助手 - 工具函数库
 * 提供通用的工具函数和页面操作方法
 */

// 避免重复声明
if (typeof window !== 'undefined' && window.Utils) {
    // 如果已经存在，直接使用
} else {

class Utils {
    /**
     * 等待元素出现
     * @param {string} selector - CSS选择器
     * @param {number} timeout - 超时时间(毫秒)
     * @param {Element} parent - 父元素，默认为document
     * @returns {Promise<Element>}
     */
    static waitForElement(selector, timeout = 10000, parent = document) {
        return new Promise((resolve, reject) => {
            const element = parent.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = parent.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(parent, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`等待元素超时: ${selector}`));
            }, timeout);
        });
    }

    /**
     * 等待元素消失
     * @param {string} selector - CSS选择器
     * @param {number} timeout - 超时时间(毫秒)
     * @returns {Promise<void>}
     */
    static waitForElementDisappear(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const checkElement = () => {
                if (!document.querySelector(selector)) {
                    resolve();
                    return;
                }
                setTimeout(checkElement, 100);
            };

            checkElement();

            setTimeout(() => {
                reject(new Error(`等待元素消失超时: ${selector}`));
            }, timeout);
        });
    }

    /**
     * 等待页面加载完成
     * @param {number} timeout - 超时时间(毫秒)
     * @returns {Promise<void>}
     */
    static waitForPageLoad(timeout = 30000) {
        return new Promise((resolve, reject) => {
            if (document.readyState === 'complete') {
                resolve();
                return;
            }

            const onLoad = () => {
                document.removeEventListener('DOMContentLoaded', onLoad);
                window.removeEventListener('load', onLoad);
                resolve();
            };

            document.addEventListener('DOMContentLoaded', onLoad);
            window.addEventListener('load', onLoad);

            setTimeout(() => {
                document.removeEventListener('DOMContentLoaded', onLoad);
                window.removeEventListener('load', onLoad);
                reject(new Error('页面加载超时'));
            }, timeout);
        });
    }

    /**
     * 安全点击元素
     * @param {string|Element} target - 选择器或元素
     * @param {number} delay - 延迟时间(毫秒)
     * @returns {Promise<boolean>}
     */
    static async safeClick(target, delay = 1000) {
        try {
            let element;
            if (typeof target === 'string') {
                element = await this.waitForElement(target, 5000);
            } else {
                element = target;
            }

            if (!element) {
                throw new Error('元素不存在');
            }

            // 滚动到元素可见
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await this.sleep(500);

            // 检查元素是否可点击
            if (element.disabled || element.style.pointerEvents === 'none') {
                throw new Error('元素不可点击');
            }

            // 模拟真实点击
            const rect = element.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top + rect.height / 2;

            element.dispatchEvent(new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y
            }));

            await this.sleep(50);

            element.dispatchEvent(new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y
            }));

            element.dispatchEvent(new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y
            }));

            await this.sleep(delay);
            return true;
        } catch (error) {
            console.error('点击失败:', error);
            return false;
        }
    }

    /**
     * 安全输入文本
     * @param {string|Element} target - 选择器或元素
     * @param {string} text - 输入文本
     * @param {number} delay - 延迟时间(毫秒)
     * @returns {Promise<boolean>}
     */
    static async safeInput(target, text, delay = 800) {
        try {
            let element;
            if (typeof target === 'string') {
                element = await this.waitForElement(target, 5000);
            } else {
                element = target;
            }

            if (!element) {
                throw new Error('元素不存在');
            }

            // 清空现有内容
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));

            // 逐字符输入
            for (let i = 0; i < text.length; i++) {
                element.value += text[i];
                element.dispatchEvent(new Event('input', { bubbles: true }));
                await this.sleep(50);
            }

            element.dispatchEvent(new Event('change', { bubbles: true }));
            await this.sleep(delay);
            return true;
        } catch (error) {
            console.error('输入失败:', error);
            return false;
        }
    }

    /**
     * 获取元素文本
     * @param {string|Element} target - 选择器或元素
     * @returns {Promise<string>}
     */
    static async getText(target) {
        try {
            let element;
            if (typeof target === 'string') {
                element = await this.waitForElement(target, 5000);
            } else {
                element = target;
            }

            return element ? element.textContent.trim() : '';
        } catch (error) {
            console.error('获取文本失败:', error);
            return '';
        }
    }

    /**
     * 检查元素是否存在
     * @param {string} selector - CSS选择器
     * @param {Element} parent - 父元素
     * @returns {boolean}
     */
    static elementExists(selector, parent = document) {
        return !!parent.querySelector(selector);
    }

    /**
     * 获取所有匹配元素
     * @param {string} selector - CSS选择器
     * @param {Element} parent - 父元素
     * @returns {NodeList}
     */
    static getAllElements(selector, parent = document) {
        return parent.querySelectorAll(selector);
    }

    /**
     * 延迟执行
     * @param {number} ms - 毫秒数
     * @returns {Promise<void>}
     */
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 随机延迟
     * @param {number} min - 最小毫秒数
     * @param {number} max - 最大毫秒数
     * @returns {Promise<void>}
     */
    static randomSleep(min, max) {
        const ms = Math.floor(Math.random() * (max - min + 1)) + min;
        return this.sleep(ms);
    }

    /**
     * 重试执行函数
     * @param {Function} fn - 要执行的函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试间隔(毫秒)
     * @returns {Promise<any>}
     */
    static async retry(fn, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                if (i < maxRetries) {
                    console.warn(`重试 ${i + 1}/${maxRetries}:`, error.message);
                    await this.sleep(delay);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 获取当前页面URL信息
     * @returns {Object}
     */
    static getPageInfo() {
        return {
            url: window.location.href,
            pathname: window.location.pathname,
            search: window.location.search,
            hash: window.location.hash,
            host: window.location.host
        };
    }

    /**
     * 检查是否为指定页面
     * @param {string} pattern - URL模式
     * @returns {boolean}
     */
    static isPage(pattern) {
        const url = window.location.href;
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(url);
        }
        return url.includes(pattern);
    }

    /**
     * 等待URL变化
     * @param {string} expectedUrl - 期望的URL
     * @param {number} timeout - 超时时间(毫秒)
     * @returns {Promise<void>}
     */
    static waitForUrlChange(expectedUrl, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const checkUrl = () => {
                if (window.location.href.includes(expectedUrl)) {
                    resolve();
                    return;
                }
                setTimeout(checkUrl, 500);
            };

            checkUrl();

            setTimeout(() => {
                reject(new Error(`URL变化超时: ${expectedUrl}`));
            }, timeout);
        });
    }

    /**
     * 获取图片的Base64编码
     * @param {string|Element} target - 图片选择器或元素
     * @returns {Promise<string>}
     */
    static async getImageBase64(target) {
        try {
            let img;
            if (typeof target === 'string') {
                img = await this.waitForElement(target, 5000);
            } else {
                img = target;
            }

            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.naturalWidth || img.width;
                canvas.height = img.naturalHeight || img.height;
                
                ctx.drawImage(img, 0, 0);
                
                try {
                    const base64 = canvas.toDataURL('image/png').split(',')[1];
                    resolve(base64);
                } catch (error) {
                    reject(error);
                }
            });
        } catch (error) {
            console.error('获取图片Base64失败:', error);
            throw error;
        }
    }

    /**
     * 格式化时间
     * @param {Date} date - 日期对象
     * @returns {string}
     */
    static formatTime(date = new Date()) {
        return date.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 生成随机字符串
     * @param {number} length - 长度
     * @returns {string}
     */
    static randomString(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 深度克隆对象
     * @param {any} obj - 要克隆的对象
     * @returns {any}
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            Object.keys(obj).forEach(key => {
                cloned[key] = this.deepClone(obj[key]);
            });
            return cloned;
        }
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function}
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function}
     */
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.Utils = Utils;
}

} // 结束条件声明
