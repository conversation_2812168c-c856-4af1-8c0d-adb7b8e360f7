<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>江西干部网络学院助手</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <img src="icons/icon32.png" alt="Logo" class="logo-img">
                    <h1 class="title">学习助手</h1>
                </div>
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">就绪</span>
                </div>
            </div>
        </header>

        <!-- Tab导航 -->
        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="accounts">
                <i class="icon-user"></i>
                <span>账号管理</span>
            </button>
            <button class="tab-btn" data-tab="settings">
                <i class="icon-settings"></i>
                <span>设置选项</span>
            </button>
            <button class="tab-btn" data-tab="logs">
                <i class="icon-log"></i>
                <span>运行日志</span>
            </button>
        </nav>

        <!-- Tab内容 -->
        <main class="tab-content">
            <!-- 账号管理 -->
            <div class="tab-panel active" id="accounts">
                <div class="card">
                    <div class="card-header">
                        <h3>账号配置</h3>
                        <div class="control-buttons">
                            <button class="btn btn-primary" id="startBtn">
                                <span class="btn-text">开始学习</span>
                                <div class="btn-particles"></div>
                            </button>
                            <button class="btn btn-secondary" id="stopBtn" disabled>
                                <span class="btn-text">停止</span>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <label for="accountsInput">账号列表 (格式: 手机号 密码)</label>
                            <textarea 
                                id="accountsInput" 
                                placeholder="*********** password123&#10;*********** password456&#10;..."
                                rows="6"
                            ></textarea>
                            <div class="input-hint">每行一个账号，用空格分隔手机号和密码</div>
                        </div>
                        
                        <div class="accounts-status" id="accountsStatus">
                            <!-- 动态生成账号状态 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设置选项 -->
            <div class="tab-panel" id="settings">
                <div class="card">
                    <div class="card-header">
                        <h3>学习设置</h3>
                    </div>
                    <div class="card-body">
                        <div class="settings-grid">
                            <div class="setting-item">
                                <label class="switch">
                                    <input type="checkbox" id="autoMute" checked>
                                    <span class="slider"></span>
                                </label>
                                <div class="setting-info">
                                    <span class="setting-title">自动静音</span>
                                    <span class="setting-desc">播放视频时自动静音</span>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label class="switch">
                                    <input type="checkbox" id="autoSwitchCourseware" checked>
                                    <span class="slider"></span>
                                </label>
                                <div class="setting-info">
                                    <span class="setting-title">自动切换课件</span>
                                    <span class="setting-desc">课件播放完成后自动切换</span>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label class="switch">
                                    <input type="checkbox" id="autoSwitchCourse" checked>
                                    <span class="slider"></span>
                                </label>
                                <div class="setting-info">
                                    <span class="setting-title">自动切换课程</span>
                                    <span class="setting-desc">课程完成后自动切换</span>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label class="switch">
                                    <input type="checkbox" id="autoSelectCourse" checked>
                                    <span class="slider"></span>
                                </label>
                                <div class="setting-info">
                                    <span class="setting-title">自动选修</span>
                                    <span class="setting-desc">自动添加选修课程</span>
                                </div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="courseCount">选课数量</label>
                            <input type="text" id="courseCount" value="5" placeholder="请输入选课数量">
                        </div>

                        <div class="input-group">
                            <label for="delayTime">延时设置 (秒)</label>
                            <input type="text" id="delayTime" value="3" placeholder="请输入延时时间">
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>百度OCR配置</h3>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <label for="baiduAK">API Key (AK)</label>
                            <input type="password" id="baiduAK" placeholder="请输入百度OCR API Key">
                        </div>
                        <div class="input-group">
                            <label for="baiduSK">Secret Key (SK)</label>
                            <input type="password" id="baiduSK" placeholder="请输入百度OCR Secret Key">
                        </div>
                        <div class="input-hint">
                            <a href="https://console.bce.baidu.com/ai/#/ai/ocr/overview/index" target="_blank">
                                获取百度OCR密钥
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 运行日志 -->
            <div class="tab-panel" id="logs">
                <div class="card">
                    <div class="card-header">
                        <h3>运行日志</h3>
                        <button class="btn btn-secondary btn-sm" id="clearLogsBtn">清除日志</button>
                    </div>
                    <div class="card-body">
                        <div class="logs-container" id="logsContainer">
                            <!-- 动态生成日志内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 消息提示 -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="popup.js"></script>
</body>
</html>
