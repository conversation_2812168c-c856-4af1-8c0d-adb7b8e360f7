/**
 * 江西干部网络学院自动学习助手 - 弹窗界面
 * 专业的多账号管理和设置界面
 */

class PopupManager {
    constructor() {
        this.currentTab = 'accounts';
        this.isRunning = false;
        this.accounts = [];
        this.settings = {};
        this.logs = [];
        
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateUI();
        this.startLogPolling();

        // 强制更新日志显示
        setTimeout(() => {
            this.updateLogs();
        }, 100);
    }

    // 加载设置
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get([
                'accounts', 'settings', 'logs', 'isRunning'
            ]);

            this.accounts = result.accounts || [];
            this.settings = {
                autoMute: true,
                autoSwitchCourseware: true,
                autoSwitchCourse: true,
                autoSelectCourse: true,
                courseCount: 10,
                delayTime: 1,
                baiduAK: '',
                baiduSK: '',
                ...result.settings
            };

            // 优先从chrome.storage加载日志
            this.logs = result.logs || [];

            // 如果chrome.storage没有日志，尝试从localStorage加载
            if (this.logs.length === 0) {
                try {
                    const localLogs = localStorage.getItem('autoLearning_logs');
                    if (localLogs) {
                        this.logs = JSON.parse(localLogs);
                    }
                } catch (e) {
                    console.warn('从localStorage加载日志失败:', e);
                }
            }

            // 如果仍然没有日志，添加一些测试日志
            if (this.logs.length === 0) {
                this.logs = [
                    {
                        id: Date.now(),
                        time: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
                        level: 'info',
                        message: '插件已启动',
                        timestamp: Date.now()
                    },
                    {
                        id: Date.now() + 1,
                        time: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
                        level: 'success',
                        message: '配置加载成功',
                        timestamp: Date.now()
                    }
                ];
            }

            this.isRunning = result.isRunning || false;
        } catch (error) {
            console.error('加载设置失败:', error);
            this.showToast('加载设置失败', 'error');
        }
    }

    // 保存设置
    async saveSettings() {
        try {
            await chrome.storage.local.set({
                accounts: this.accounts,
                settings: this.settings,
                logs: this.logs,
                isRunning: this.isRunning
            });
            this.showToast('设置已保存', 'success');
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showToast('保存设置失败', 'error');
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // Tab切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);

                // 如果切换到日志页面，强制更新日志
                if (tab === 'logs') {
                    setTimeout(() => {
                        this.updateLogs();
                    }, 50);
                }
            });
        });

        // 账号输入
        const accountsInput = document.getElementById('accountsInput');
        accountsInput.addEventListener('input', (e) => {
            this.parseAccounts(e.target.value);
        });

        // 控制按钮
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startLearning();
        });

        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stopLearning();
        });

        // 设置项
        this.setupSettingsListeners();

        // 清除日志
        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            this.clearLogs();
        });
    }

    // 设置项监听器
    setupSettingsListeners() {
        const settingElements = {
            autoMute: document.getElementById('autoMute'),
            autoSwitchCourseware: document.getElementById('autoSwitchCourseware'),
            autoSwitchCourse: document.getElementById('autoSwitchCourse'),
            autoSelectCourse: document.getElementById('autoSelectCourse'),
            courseCount: document.getElementById('courseCount'),
            delayTime: document.getElementById('delayTime'),
            baiduAK: document.getElementById('baiduAK'),
            baiduSK: document.getElementById('baiduSK')
        };

        Object.entries(settingElements).forEach(([key, element]) => {
            if (element) {
                const eventType = element.type === 'checkbox' ? 'change' : 'input';
                element.addEventListener(eventType, (e) => {
                    const value = element.type === 'checkbox' ? e.target.checked : e.target.value;
                    this.updateSetting(key, value);
                });
            }
        });
    }

    // 切换Tab
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // 更新面板显示
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.toggle('active', panel.id === tabName);
        });

        this.currentTab = tabName;

        // 如果切换到日志页面，滚动到底部
        if (tabName === 'logs') {
            setTimeout(() => {
                const logsContainer = document.getElementById('logsContainer');
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }, 100);
        }
    }

    // 解析账号
    parseAccounts(text) {
        const lines = text.trim().split('\n').filter(line => line.trim());
        this.accounts = lines.map(line => {
            const [phone, password] = line.trim().split(/\s+/);
            return {
                phone: phone || '',
                password: password || '',
                status: 'ready',
                progress: 0
            };
        }).filter(account => account.phone && account.password);

        this.updateAccountsStatus();
        this.saveSettings();
    }

    // 更新账号状态显示
    updateAccountsStatus() {
        const container = document.getElementById('accountsStatus');
        if (this.accounts.length === 0) {
            container.innerHTML = '<div class="input-hint">请输入账号信息</div>';
            return;
        }

        container.innerHTML = this.accounts.map(account => `
            <div class="account-item">
                <span class="account-phone">${account.phone}</span>
                <span class="account-status status-${account.status}">
                    ${this.getStatusText(account.status)}
                </span>
            </div>
        `).join('');
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            ready: '就绪',
            running: '学习中',
            completed: '已完成',
            error: '错误'
        };
        return statusMap[status] || status;
    }

    // 更新设置
    updateSetting(key, value) {
        // 对数值类型进行验证和转换
        if (key === 'courseCount') {
            const num = parseInt(value);
            if (isNaN(num) || num < 1 || num > 20) {
                this.showToast('选课数量必须是1-20之间的数字', 'warning');
                return;
            }
            value = num;
        } else if (key === 'delayTime') {
            const num = parseInt(value);
            if (isNaN(num) || num < 1 || num > 10) {
                this.showToast('延时设置必须是1-10之间的数字', 'warning');
                return;
            }
            value = num;
        }

        this.settings[key] = value;
        this.saveSettings();
    }

    // 更新UI
    updateUI() {
        // 更新账号输入框
        const accountsInput = document.getElementById('accountsInput');
        accountsInput.value = this.accounts.map(acc => `${acc.phone} ${acc.password}`).join('\n');

        // 更新设置项
        Object.entries(this.settings).forEach(([key, value]) => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });

        // 更新按钮状态
        this.updateButtonStates();
        
        // 更新账号状态
        this.updateAccountsStatus();
        
        // 更新日志
        this.updateLogs();
        
        // 更新状态指示器
        this.updateStatusIndicator();
    }

    // 更新按钮状态
    updateButtonStates() {
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        
        startBtn.disabled = this.isRunning || this.accounts.length === 0;
        stopBtn.disabled = !this.isRunning;
    }

    // 更新状态指示器
    updateStatusIndicator() {
        const indicator = document.getElementById('statusIndicator');
        const dot = indicator.querySelector('.status-dot');
        const text = indicator.querySelector('.status-text');
        
        if (this.isRunning) {
            dot.style.background = '#f59e0b';
            text.textContent = '运行中';
        } else {
            dot.style.background = '#4ade80';
            text.textContent = '就绪';
        }
    }

    // 开始学习
    async startLearning() {
        if (this.accounts.length === 0) {
            this.showToast('请先配置账号信息', 'warning');
            return;
        }

        if (!this.settings.baiduAK || !this.settings.baiduSK) {
            this.showToast('请先配置百度OCR密钥', 'warning');
            this.switchTab('settings');
            return;
        }

        this.isRunning = true;
        await this.saveSettings();
        this.updateButtonStates();
        this.updateStatusIndicator();

        // 发送消息给background script开始学习
        try {
            await chrome.runtime.sendMessage({
                action: 'startLearning',
                accounts: this.accounts,
                settings: this.settings
            });
            this.showToast('开始自动学习', 'success');
        } catch (error) {
            console.error('启动失败:', error);
            this.showToast('启动失败', 'error');
            this.isRunning = false;
            this.updateButtonStates();
            this.updateStatusIndicator();
        }
    }

    // 停止学习
    async stopLearning() {
        this.isRunning = false;
        await this.saveSettings();
        this.updateButtonStates();
        this.updateStatusIndicator();

        try {
            await chrome.runtime.sendMessage({ action: 'stopLearning' });
            this.showToast('已停止学习', 'info');
        } catch (error) {
            console.error('停止失败:', error);
        }
    }

    // 清除日志
    async clearLogs() {
        this.logs = [];

        // 同时清除localStorage
        try {
            localStorage.removeItem('autoLearning_logs');
        } catch (e) {
            console.warn('清除localStorage失败:', e);
        }

        await this.saveSettings();
        this.updateLogs();
        this.showToast('日志已清除', 'info');
    }

    // 获取级别图标
    getLevelIcon(level) {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌'
        };
        return icons[level] || icons.info;
    }

    // 获取日志行样式
    getLogRowStyle(level) {
        const styles = {
            info: 'background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.03) 100%); border-left: 3px solid #3b82f6;',
            success: 'background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.03) 100%); border-left: 3px solid #10b981;',
            warning: 'background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.03) 100%); border-left: 3px solid #f59e0b;',
            error: 'background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.03) 100%); border-left: 3px solid #ef4444;'
        };
        return styles[level] || styles.info;
    }

    // 更新日志显示
    updateLogs() {
        const container = document.getElementById('logsContainer');
        if (!container) {
            return;
        }

        if (this.logs.length === 0) {
            container.innerHTML = '<div class="popup-log-empty">暂无日志记录</div>';
            return;
        }

        const logHtml = this.logs.slice(-100).map(log => `
            <div class="popup-log-entry ${log.level}">
                <div class="popup-log-content">
                    <span class="popup-log-time">${log.time}</span>
                    <span class="popup-log-message">${this.escapeHtml(log.message)}</span>
                </div>
            </div>
        `).join('');

        container.innerHTML = logHtml;

        // 滚动到底部
        setTimeout(() => {
            container.scrollTop = container.scrollHeight;
        }, 10);
    }

    // 获取级别颜色
    getLevelColor(level) {
        const colors = {
            info: '#3b82f6',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };
        return colors[level] || colors.info;
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 开始日志轮询
    startLogPolling() {
        // 立即执行一次
        this.pollLogs();

        // 然后每秒轮询
        setInterval(() => {
            this.pollLogs();
        }, 1000);
    }

    // 轮询日志数据
    async pollLogs() {
        try {
            let logs = [];
            let isRunning = false;

            // 优先从chrome.storage获取
            try {
                const result = await chrome.storage.local.get(['logs', 'isRunning']);
                logs = result.logs || [];
                isRunning = result.isRunning || false;
            } catch (e) {
                console.warn('从chrome.storage获取数据失败:', e);
            }

            // 如果chrome.storage没有日志，从localStorage获取
            if (logs.length === 0) {
                try {
                    const localLogs = localStorage.getItem('autoLearning_logs');
                    if (localLogs) {
                        logs = JSON.parse(localLogs);
                    }
                } catch (e) {
                    console.warn('从localStorage获取日志失败:', e);
                }
            }

            // 更新日志
            if (logs.length !== this.logs.length || JSON.stringify(logs) !== JSON.stringify(this.logs)) {
                this.logs = logs;
                if (this.currentTab === 'logs') {
                    this.updateLogs();
                }
            }

            // 更新运行状态
            if (isRunning !== this.isRunning) {
                this.isRunning = isRunning;
                this.updateButtonStates();
                this.updateStatusIndicator();
            }
        } catch (error) {
            console.error('日志轮询失败:', error);
        }
    }

    // 显示提示消息
    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toast.innerHTML = `
            <span class="toast-icon">${icons[type] || icons.info}</span>
            <span class="toast-message">${message}</span>
            <button class="toast-close">×</button>
        `;

        // 关闭按钮
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });

        container.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
