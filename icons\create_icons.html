<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .download-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #4facfe;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .download-btn:hover {
            background: #3b82f6;
        }
    </style>
</head>
<body>
    <h1>江西干部网络学院助手 - 图标生成器</h1>
    <div class="icon-container">
        <div class="icon-item">
            <h3>16x16</h3>
            <canvas id="icon16" width="16" height="16"></canvas>
            <br>
            <button class="download-btn" onclick="downloadIcon('icon16', 'icon16.png')">下载</button>
        </div>
        <div class="icon-item">
            <h3>32x32</h3>
            <canvas id="icon32" width="32" height="32"></canvas>
            <br>
            <button class="download-btn" onclick="downloadIcon('icon32', 'icon32.png')">下载</button>
        </div>
        <div class="icon-item">
            <h3>48x48</h3>
            <canvas id="icon48" width="48" height="48"></canvas>
            <br>
            <button class="download-btn" onclick="downloadIcon('icon48', 'icon48.png')">下载</button>
        </div>
        <div class="icon-item">
            <h3>128x128</h3>
            <canvas id="icon128" width="128" height="128"></canvas>
            <br>
            <button class="download-btn" onclick="downloadIcon('icon128', 'icon128.png')">下载</button>
        </div>
    </div>

    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4facfe');
            gradient.addColorStop(1, '#00f2fe');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制书本图标
            ctx.fillStyle = 'white';
            const bookWidth = size * 0.5;
            const bookHeight = size * 0.4;
            const bookX = (size - bookWidth) / 2;
            const bookY = (size - bookHeight) / 2;
            
            // 书本主体
            ctx.fillRect(bookX, bookY, bookWidth, bookHeight);
            
            // 书本线条
            ctx.strokeStyle = '#4facfe';
            ctx.lineWidth = Math.max(1, size / 32);
            
            // 横线
            for (let i = 1; i <= 3; i++) {
                const y = bookY + (bookHeight / 4) * i;
                ctx.beginPath();
                ctx.moveTo(bookX + bookWidth * 0.1, y);
                ctx.lineTo(bookX + bookWidth * 0.9, y);
                ctx.stroke();
            }
            
            // 添加学习符号 (小圆点)
            ctx.fillStyle = '#00f2fe';
            const dotSize = Math.max(2, size / 16);
            ctx.beginPath();
            ctx.arc(bookX + bookWidth * 0.8, bookY + bookHeight * 0.2, dotSize, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 生成所有尺寸的图标
        createIcon('icon16', 16);
        createIcon('icon32', 32);
        createIcon('icon48', 48);
        createIcon('icon128', 128);
        
        // 自动下载所有图标
        setTimeout(() => {
            downloadIcon('icon16', 'icon16.png');
            setTimeout(() => downloadIcon('icon32', 'icon32.png'), 100);
            setTimeout(() => downloadIcon('icon48', 'icon48.png'), 200);
            setTimeout(() => downloadIcon('icon128', 'icon128.png'), 300);
        }, 1000);
    </script>
</body>
</html>
